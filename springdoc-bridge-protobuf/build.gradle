description = "SpringDoc Bridge for Protocol Buffers"

dependencies {
    compileOnly("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    compileOnly("org.springdoc:springdoc-openapi-starter-common:${springDocsVersion}")

    api(project(":jackson-module-protobuf"))

    // Test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")
    testImplementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    testImplementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:${springDocsVersion}")
}

apply from: "${rootDir}/gradle/protobuf.gradle"
apply from: "${rootDir}/gradle/deploy.gradle"
