group=io.github.danielliu1123
version=0.4.1-SNAPSHOT

# Spring related
# https://github.com/spring-projects/spring-boot
springBootVersion=4.0.0-M2
# https://github.com/springdoc/springdoc-openapi
springDocsVersion=2.8.13
# https://central.sonatype.com/artifact/io.grpc/grpc-protobuf/dependencies
protobufVersion=3.25.8
# https://central.sonatype.com/artifact/com.fasterxml.jackson.core/jackson-databind
jacksonVersion=2.20.0
jackson3Version=3.0.0-rc9
# https://central.sonatype.com/artifact/org.projectlombok/lombok
lombokVersion=1.18.40
# https://github.com/google/protobuf-gradle-plugin
protobufGradlePluginVersion=0.9.5

# Code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=7.2.1
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=6.3.0
# https://github.com/spotbugs/spotbugs-gradle-plugin/blob/master/build.gradle.kts#L28
spotbugsAnnotationsVersion=4.9.4

# Publishing
# https://github.com/jreleaser/jreleaser
jReleaserVersion=1.20.0

org.gradle.jvmargs=-Xmx2g
org.gradle.parallel=true
org.gradle.caching=true
