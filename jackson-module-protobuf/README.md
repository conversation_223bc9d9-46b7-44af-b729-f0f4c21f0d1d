# Jackson Module - Protocol Buffers

**Jackson Module Protobuf** provides comprehensive Jackson serialization and deserialization support for [Protocol Buffers](https://protobuf.dev/) messages and enums, following the official [Protobuf JSON Mapping](https://protobuf.dev/programming-guides/json/) specification.

**Supports both Jackson 2.x and Jackson 3.x** - The module automatically detects and works with both versions of Jackson.

## 🎯 Features

- **🔄 Bidirectional Conversion**: Serialize protobuf to JSON and deserialize JSON to protobuf
- **📋 Well-Known Types**: Full support for `Timestamp`, `Duration`, `Any`, `Struct`, `Value`, etc.
- **🏷️ Enum Support**: Configurable enum serialization (string names or integer values)
- **⚙️ Flexible Configuration**: Customizable `JsonFormat.Parser` and `JsonFormat.Printer`
- **🚀 High Performance**: Optimized serialization with caching for better performance
- **📖 Spec Compliant**: Follows Google's official protobuf JSON mapping rules

## 🚀 Installation

### Maven

```xml
<dependency>
    <groupId>io.github.danielliu1123</groupId>
    <artifactId>jackson-module-protobuf</artifactId>
    <version>${jackson-module-protobuf.version}</version>
</dependency>
```

### Gradle

```groovy
implementation "io.github.danielliu1123:jackson-module-protobuf:${jacksonModuleProtobufVersion}"
```

## 📖 Usage Examples

### Basic Usage

#### Jackson 2.x

```java
import com.fasterxml.jackson.databind.json.JsonMapper;
import jacksonmodule.protobuf.ProtobufModule;

// Register the module with JsonMapper
ObjectMapper mapper = JsonMapper.builder()
    .addModule(new ProtobufModule())
    .build();

// Serialize protobuf message to JSON
User user = User.newBuilder()
    .setUserId("user-123")
    .setUsername("john_doe")
    .setEmail("<EMAIL>")
    .setStatus(User.UserStatus.ACTIVE)
    .setCreatedAt(Timestamps.now())
    .build();

String json = mapper.writeValueAsString(user);
User restored = mapper.readValue(json, User.class);
```

#### Jackson 3.x

```java
import tools.jackson.databind.json.JsonMapper;
import jacksonmodule.protobuf.v3.ProtobufModule;

// Register the module with JsonMapper
ObjectMapper mapper = JsonMapper.builder()
    .addModule(new ProtobufModule())
    .build();

// Usage is the same as Jackson 2.x
User user = User.newBuilder()
    .setUserId("user-123")
    .setUsername("john_doe")
    .setEmail("<EMAIL>")
    .setStatus(User.UserStatus.ACTIVE)
    .setCreatedAt(Timestamps.now())
    .build();

String json = mapper.writeValueAsString(user);
User restored = mapper.readValue(json, User.class);
```

### Custom Configuration

```java
import com.google.protobuf.util.JsonFormat;
import jacksonmodule.protobuf.ProtobufModule;
// import jacksonmodule.protobuf.v3.ProtobufModule; // for Jackson 3.x

// Create custom options
ProtobufModule.Options options = ProtobufModule.Options.builder()
    .parser(JsonFormat.parser()
        .ignoringUnknownFields()
        .usingRecursionLimit(100))
    .printer(JsonFormat.printer()
        .omittingInsignificantWhitespace()
        .includingDefaultValueFields())
    .build();

// Register module with custom options
ObjectMapper mapper = new ObjectMapper();
mapper.registerModule(new ProtobufModule(options));
```

### Spring Boot Integration

```java
@Configuration(proxyBeanMethods = false)
public class JacksonConfig {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer protobufCustomizer() {
        return builder -> builder.modules(new ProtobufModule());
    }
}
```

## 🧪 Testing

```bash
./gradlew :jackson-module-protobuf:test
```

## 🔗 Related Links

- [Jackson Documentation](https://github.com/FasterXML/jackson-docs)
- [Protocol Buffers](https://protobuf.dev/)
- [Protobuf JSON Mapping](https://protobuf.dev/programming-guides/json/)
- [SpringDoc Bridge Protobuf](../springdoc-bridge-protobuf) - SpringDoc integration using this module
